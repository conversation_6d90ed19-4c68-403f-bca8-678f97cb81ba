<?php
/**
 * Template Name: Other Services Page
 * Description: Custom Other Services page template for Krystelis_Custom Theme
 *
 * @package Krystelis_Custom
 */

get_header();
?>
<div class="other-services-top">
  <div class="other-services-subheading">Making clinical research crystal clear</div>
  <div class="other-services-heading">Other services include</div>
  <div class="other-services-cards">
    <div class="other-services-card other-services-card-tech">
      <div class="other-services-card-icon">
        <!-- Gear Icon -->
        <!-- (SVG remains same) -->
        <div class="navigation-container">
        <div class="nav-cards">
            <div class="nav-card" onclick="selectCard(this)">
                <div class="card-content">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/technology-icon.png" alt="Health Communication" class="nav-icon" />
                    <h2 class="nav-title">Technology</h2>
                </div>
            </div>

            <div class="nav-card" onclick="selectCard(this)">
                <div class="card-content">
                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/consulting-icon.png" alt="Public Communication" class="nav-icon" />
                    <h2 class="nav-title">Consulting</h2>
                </div>
            </div>

            <div class="nav-card" onclick="selectCard(this)">
                <div class="card-content">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/admin-services-icon.png" alt="Public Health" class="nav-icon" />

                    <h2 class="nav-title">Administrative Services</h2>
                </div>
            </div>
        </div>
    </div>

<div class="other-services-main">
  <div class="other-services-content">
    <div class="other-services-left">
      <h1 class="other-services-title">Technology</h1>
      <p class="other-services-text">
      The impact of technology on drug development activities has never been greater. Krystelis continues to invest in and apply technology solutions that enhance the value of our service offerings.
      </p>
    </div>
    <div class="other-services-right">
      <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Technology.png" alt="Consulting team" class="other-services-img" />
    </div>
  </div>
</div>
        <!-- Services Section -->
        <div class="services-grid">
            <div class="service-item">
                <h2>KrystelisClearAI</h2>
                <p>The power of AI to transform business activities is unquestionable. Krystelis has harnessed the potential of large language models (LLM) to develop and implement a tool that can be customised to create consistently and efficiently any type of plain language document aimed at a particular target reading level.</p>
            </div>

            <div class="service-item">
                <h2>KrystelisClearPM</h2>
                <p>Robust project management underpins excellent service delivery. This includes timeline and resource management, cross-team communication and generation of appropriate performance indicators. Krystelis has deployed a web-based tool developed using a commercially available platform that manages these aspects through a simple interface. Access to this is provided to clients who can interact with our teams and monitor progress against deliverables and KQIs/KPIs in real time.</p>
            </div>

            <div class="service-item">
                <h2>Document anonymisation</h2>
                <p>While Adobe Acrobat is suitable for redaction of documents, the more complex transformation approach to anonymisation benefits from the application of a purpose-built tool. The Krystelis team has for several years worked with one of the leading providers of anonymisation technology and we apply this solution to relevant dataset and document anonymisation projects.</p>
            </div>
        </div>

        <!-- Consulting Services Section -->
        <div class="consulting-section">
            <div class="container">
                <div class="consulting-content">
                    <div class="consulting-text">
                        <h2>Consulting services</h2>
                        <p>Krystelis' services are based on our team's comprehensive understanding of current and upcoming regulatory requirements across all regions in which our clients operate. Several of our team members are recognised as global subject matter experts in their fields, for example, in clinical trial transparency and plain language writing. They contribute to the shaping of future regulations by producing thought papers, participating in industry forums, and contributing to proposed guidance from health authorities. We apply this knowledge not just to deliver our services, but also to help our clients build and adapt their internal processes in response to the continuously evolving regulatory landscape.</p>
                    </div>
                    <div class="consulting-image">
                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Consulting-Services-Image.png" alt="Consulting-Services" />
                    </div>
                </div>
            </div>
        </div>
    </div>
        <!-- Header Text -->
        <div class="header-text">
            Krystelis is ideally positioned to provide regulatory consulting services in our areas of expertise, either alongside our operational services or as a standalone engagement.
        </div>

        <!-- Main Title -->
        <h1 class="main-title">These services could include</h1>

        <!-- Services Grid -->
        <div class="services-grid">
            <div class="service-box">
                <div class="service-icon pattern-1"></div>
                <div class="service-text">
                    Assessing how regulatory changes impact business processes, for example, the introduction of EU–CTR 536/2014
                </div>
            </div>

            <div class="service-box">
                <div class="service-icon pattern-2"></div>
                <div class="service-text">
                    How to adapt processes in the most efficient way to ensure compliance with regulations
                </div>
            </div>

            <div class="service-box">
                <div class="service-icon pattern-3"></div>
                <div class="service-text">
                    Process design, including writing SOPs and templates
                </div>
            </div>

            <div class="service-box">
                <div class="service-icon pattern-4"></div>
                <div class="service-text">
                    Implementation plans and change management including training on new regulations and associated processes
                </div>
            </div>
        </div>
    </div>

    <!-- advantages-of-using-pharmacovigilance -->
<section class="advantages-of-using-pharmacovigilance">
  <div class="advantages-of-using-pharmacovigilance-bg-top">
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Section-Bottom-Background.png" alt="Background Top" class="pharma-bg-top-img" />
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Section-Bottom-Bg-shape.png" alt="Background Corner" class="pharma-bg-corner-img" />
  </div>
</section>

<div class="services-section">
        <!-- <div class="container"> -->
            <div class="services-grid">
                <div class="service-section">
                    <h2>
                        <svg class="service-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="2" y="2" width="20" height="20" rx="2" ry="2"/>
                            <path d="M7 2v20M17 2v20M2 12h20"/>
                        </svg>
                        Systems data entry and administration, for example
                    </h2>
                    <div class="service-item">
                        <div class="service-bullet"></div>
                        <div class="service-text">Clinical Trial Information Systems (CTIS)</div>
                    </div>
                    <div class="service-item">
                        <div class="service-bullet"></div>
                        <div class="service-text">Clinical Trial Management Systems (CTMS)</div>
                    </div>
                    <div class="service-item">
                        <div class="service-bullet"></div>
                        <div class="service-text">Learning Management Systems (LMS)</div>
                    </div>
                </div>

                <div class="service-section">
                    <h2>
                        <svg class="service-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                            <polyline points="22,6 12,13 2,6"/>
                        </svg>
                        Mailbox monitoring, triage, and workflow management
                    </h2>
                    <div class="service-item">
                        <div class="service-bullet"></div>
                        <div class="service-text">Investigator-Initiated Study Management and e-Requests Management</div>
                    </div>
                    <div class="service-item">
                        <div class="service-bullet"></div>
                        <div class="service-text">Query Handling from Investigators, Health Care Providers (HCP), or Patients (e.g. patient enquiries received through clinicaltrials.gov)</div>
                    </div>
                </div>

                <div class="service-section">
                    <h2>
                        <svg class="service-icon-svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        User acceptance testing (UAT)
                    </h2>
                    <div class="service-item">
                        <div class="service-bullet"></div>
                        <div class="service-text">Work with your system owners to develop UAT scripts</div>
                    </div>
                    <div class="service-item">
                        <div class="service-bullet"></div>
                        <div class="service-text">Execute UAT scripts</div>
                    </div>
                </div>
            </div>
        <!-- </div> -->
    </div>

    <!-- Benefits of outsourcing administrative services to Krystelis -->
<!-- Experience of Our Experts Section Start -->
<section class="expertise-section">
  <h2 class="expertise-heading">Experience of our experts</h2>
  <div class="expertise-grid">
    <div class="expertise-card">
      <div class="expertise-icon-wrapper">
<svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>      </div>
      <div class="expertise-description">Completed hundreds of plain language communication materials</div>
    </div>
    <div class="expertise-card">
      <div class="expertise-icon-wrapper">
<svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>      </div>
      <div class="expertise-description">Delivered both content and graphic design support</div>
    </div>
    <div class="expertise-card">
      <div class="expertise-icon-wrapper">
<svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>      </div>
      <div class="expertise-description">Implemented plain language processes for study sponsors</div>
    </div>
    <div class="expertise-card">
      <div class="expertise-icon-wrapper">
<svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>      </div>
      <div class="expertise-description">Trained clinical study teams on plain language concepts and processes</div>
    </div>
    <div class="expertise-card">
      <div class="expertise-icon-wrapper">
<svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>      </div>
      <div class="expertise-description">Published articles on key developments in the plain language domain</div>
    </div>
    <div class="expertise-card">
      <div class="expertise-icon-wrapper">
<svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>      </div>
      <div class="expertise-description">Contributed to the development of industry standards</div>
    </div>
  </div>
</section>

<section class="other-services-cta">
  <div class="other-services-cta-bg">
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/CTA-Bg-Shape-Orange-1024x1024.png" alt="Decorative Orange Triangle" class="other-services-cta-triangle other-services-cta-triangle-left" />
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/CTA-Bg-Shape-Blue-1024x1024.png" alt="Decorative Blue Triangle" class="other-services-cta-triangle other-services-cta-triangle-right" />
    <div class="other-services-cta-content">
      <div class="other-services-cta-subtitle">INTERESTED IN LEARNING MORE? </div>
      <div class="other-services-cta-title">Get In Touch</div>
      <a href="http://localhost/wordpress/contact-us/" class="other-services-cta-btn">CONTACT US</a>
    </div>
  </div>
</section>


<style>
    .navigation-container {
        
            width: 100%;
            margin: 0 auto;
        }

        .nav-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            width: 100%;
            margin-bottom: 50px;
        }

        .nav-card {
            background: linear-gradient(135deg, #1e88e5 0%, #1976d2 100%);
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(30, 136, 229, 0.3);
            border: 2px solid transparent;
        }

        .nav-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #FF6A18 0%, #c96c1f 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
            z-index: 1;
        }

        .nav-card:hover::before {
            opacity: 1;
        }

        .nav-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(212, 120, 42, 0.4);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .card-content {
            position: relative;
            z-index: 2;
        }

        .nav-icon {
            width: 120px;
            height: 120px;
            margin: -35px auto 25px;
            fill: white;
            transition: transform 0.3s ease;
        }

        .nav-card:hover .nav-icon {
            transform: scale(1.1);
        }

        .nav-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: white;
            margin: 0;
            letter-spacing: 0.5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .nav-card.active::before {
            opacity: 0;
        }

        @media (max-width: 768px) {
            body {
                padding: 30px 15px;
            }

            .nav-cards {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .nav-card {
                padding: 30px 20px;
                border-radius: 16px;
            }

            .nav-icon {
                width: 60px;
                height: 60px;
                margin-bottom: 18px;
            }

            .nav-title {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 20px 10px;
            }

            .nav-card {
                padding: 25px 15px;
                border-radius: 14px;
            }

            .nav-icon {
                width: 50px;
                height: 50px;
                margin-bottom: 15px;
            }

            .nav-title {
                font-size: 1.3rem;
                letter-spacing: 0.3px;
            }
        }

.other-services-top {
  width: 100vw;
  max-width: 100%;
  margin: 0 auto 32px auto;
  padding: 0 0 24px 0;
  background: #fff;
}
.other-services-subheading {
  color: #FF6A18;
  font-family: "Maven Pro", sans-serif;
  font-size: 1.50rem;
  font-weight: 500;
  margin: 0 0 12px 20px;
  padding-left: 16px;
  animation: revealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both;
}
@keyframes revealDown {
  0% {
    opacity: 0;
    transform: translateY(-40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.other-services-heading {
  font-family: "Maven Pro", sans-serif;
  font-size: 48px;
  font-weight: 600;
  color: #0072DA;
  margin: 30px 0 30px 30px;
  line-height: 1.2;
}
.other-services-cards {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  border-radius: 4px;
  padding: 15px 0;
  gap: 0;
}
.other-services-card {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 12px 0;
  transition: background 0.2s, box-shadow 0.2s;
}
.other-services-card-icon {
  margin-bottom: 10px;
}
.other-services-card-label {
  color: #FF6A18;
  font-family: "Maven Pro", sans-serif;
  font-size: 1.25rem;
  font-weight: 500;
  letter-spacing: 1px;
  margin-top: 4px;
}
.other-services-main {
  width: 100vw;
  max-width: 100%;
  background: #fff;
  padding: 0 0 40px 0;
  margin: 0 auto;
}
.other-services-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 500px;
  max-width: 100%;
  margin: 0;
  padding: 0;
  gap: 0;
}
.other-services-left {
  min-width: 260px;
  margin-left: 30px;
  position: relative;
  opacity: 0;
  transform: translateY(40px);
  animation: revealUp 1s cubic-bezier(0.23, 1, 0.32, 1) 0.3s both;
}
@keyframes revealUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.other-services-title {
  font-family: "Maven Pro", sans-serif;
  font-size: 52px;
  font-weight: 600;
  color: #0072DA;
  margin: 30px 0 30px 0;
  line-height: 1.2;
}
.other-services-text {
  font-family: Georgia, serif;
  font-size: 1.25rem;
  color: #525252;
  line-height: 1.5;
  margin: 0 0 20px 0;
}
.other-services-right {
  flex: 1 1 0;
  align-items: flex-start;
  margin-left: 70px;
}
.other-services-img {
  width: 100%;
  max-width: 650px;
  border-radius: 18px;
  object-fit: cover;
  box-shadow: 0 4px 24px rgba(0,0,0,0.07);
  opacity: 0;
  transform: scale(0.85);
  animation: zoomInImg 1s cubic-bezier(0.23, 1, 0.32, 1) 0.6s both;
}
@keyframes zoomInImg {
  0% {
    opacity: 0;
    transform: scale(0.85);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
@media (max-width: 900px) {
  .other-services-cards {
    flex-direction: column;
    align-items: stretch;
    padding: 0;
    gap: 0;
  }
  .other-services-card {
    border-bottom: 1px solid #eee;
    border-radius: 0;
    padding: 18px 0;
  }
  .other-services-card:last-child {
    border-bottom: none;
  }
  .other-services-content {
    flex-direction: column;
    gap: 24px;
    padding: 24px 8px 0 8px;
  }
  .other-services-right {
    justify-content: center;
  }
  .other-services-img {
    max-width: 95vw;
  }
}
@media (max-width: 600px) {
  .other-services-subheading {
    font-size: 1.2rem;
    padding-left: 8px;
  }
  .other-services-cards {
    margin: 0 2px;
    padding: 0;
  }
  .other-services-title {
    font-size: 2rem;
  }
  .other-services-text {
    font-size: 1rem;
  }
  .other-services-img {
    border-radius: 10px;
  }
}
.container {
        
            margin: 0 auto;
            padding: 40px 20px;
        }

        /* Services Section */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }

        .service-item {
            padding: 0;
        }

        .service-item h2 {
            color: #2563eb;
            font-size: 32px;
            font-weight: 500;
            margin-bottom: 15px;
        }

        .service-item p {
            color: #525252;
            font-size: 20px;
            line-height: 1.7;
        }

        /* Consulting Services Section */
        .consulting-section {
            padding-left: 20px;
            padding-right: 20px;
        }

        .consulting-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: center;
        }
        .consulting-text{
            margin-right: 20px;
        }

        .consulting-text h2 {
            color: #0078DA;
            font-size: 48px;
            font-weight: 600;
            margin-bottom: 25px;
        }

        .consulting-text p {
            color: #525252;
            font-size: 20px;
        }

        .consulting-image {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
        }

        .consulting-image img {
            width: 100%;
            height: 550px;
            object-fit: cover;
            margin-top: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        /* Responsive Design */
        
        /* Large Desktop (1200px and above) */
        @media (min-width: 1200px) {
            .container {
                padding: 50px 30px;
            }
            
            .services-grid {
                gap: 50px;
            }
            
            .service-item h2 {
                font-size: 1.6rem;
            }
        }

        /* Desktop (992px to 1199px) */
        @media (min-width: 992px) and (max-width: 1199px) {
            .container {
                padding: 40px 25px;
            }
            
            .services-grid {
                gap: 40px;
            }
            
            .service-item h2 {
                font-size: 1.5rem;
            }
            
            .consulting-text h2 {
                font-size: 2rem;
            }
            
            .consulting-image img {
                height: 350px;
            }
        }

        /* Tablet (768px to 991px) */
        @media (min-width: 768px) and (max-width: 991px) {
            .container {
                padding: 35px 20px;
            }

            .services-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 35px;
                margin-bottom: 50px;
            }

            .service-item h2 {
                font-size: 1.4rem;
            }

            .service-item p {
                font-size: 0.9rem;
            }

            .consulting-content {
                grid-template-columns: 1fr;
                gap: 35px;
            }

            .consulting-text h2 {
                font-size: 1.8rem;
            }

            .consulting-text p {
                font-size: 0.95rem;
            }

            .consulting-image img {
                height: 300px;
            }
        }

        /* Mobile Large (576px to 767px) */
        @media (min-width: 576px) and (max-width: 767px) {
            .container {
                padding: 30px 20px;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 30px;
                margin-bottom: 45px;
            }

            .service-item h2 {
                font-size: 1.3rem;
                margin-bottom: 12px;
            }

            .service-item p {
                font-size: 0.9rem;
                line-height: 1.6;
            }

            .consulting-content {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .consulting-text h2 {
                font-size: 1.6rem;
                margin-bottom: 20px;
            }

            .consulting-text p {
                font-size: 0.9rem;
                line-height: 1.7;
            }

            .consulting-image img {
                height: 250px;
            }
        }

        /* Mobile Small (up to 575px) */
        @media (max-width: 575px) {
            .container {
                padding: 25px 15px;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 25px;
                margin-bottom: 40px;
            }

            .service-item h2 {
                font-size: 1.2rem;
                margin-bottom: 10px;
            }

            .service-item p {
                font-size: 0.85rem;
                line-height: 1.5;
            }

            .consulting-content {
                grid-template-columns: 1fr;
                gap: 25px;
            }

            .consulting-text h2 {
                font-size: 1.4rem;
                margin-bottom: 15px;
            }

            .consulting-text p {
                font-size: 0.85rem;
                line-height: 1.6;
            }

            .consulting-image img {
                height: 200px;
            }
        }

        /* Extra Small Mobile (up to 480px) */
        @media (max-width: 480px) {
            .container {
                padding: 20px 12px;
            }

            .services-grid {
                gap: 20px;
                margin-bottom: 35px;
            }

            .service-item h2 {
                font-size: 1.1rem;
                margin-bottom: 8px;
            }

            .service-item p {
                font-size: 0.8rem;
                line-height: 1.4;
            }

            .consulting-content {
                gap: 20px;
            }

            .consulting-text h2 {
                font-size: 1.3rem;
                margin-bottom: 12px;
            }

            .consulting-text p {
                font-size: 0.8rem;
                line-height: 1.5;
            }

            .consulting-image img {
                height: 180px;
            }
        }

        /* Ultra Small Mobile (up to 360px) */
        @media (max-width: 360px) {
            .container {
                padding: 15px 10px;
            }

            .services-grid {
                gap: 15px;
                margin-bottom: 30px;
            }

            .service-item h2 {
                font-size: 1rem;
                margin-bottom: 6px;
            }

            .service-item p {
                font-size: 0.75rem;
                line-height: 1.3;
            }

            .consulting-content {
                gap: 15px;
            }

            .consulting-text h2 {
                font-size: 1.2rem;
                margin-bottom: 10px;
            }

            .consulting-text p {
                font-size: 0.75rem;
                line-height: 1.4;
            }

            .consulting-image img {
                height: 160px;
            }
        }

        .container {
            
            margin: 0 auto;
            padding: 60px 20px;
            position: relative;
            z-index: 2;
        }

        /* Header Text */
        .header-text {
            text-align: center;
            color: #ff6b35;
            font-size: 22px;
            font-weight: 500;
            margin-bottom: 30px;
            line-height: 1.5;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Main Title */
        .main-title {
            text-align: center;
            color: #2563eb;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 60px;
            position: relative;
        }

        /* Services Grid */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin: 0 auto;
        }

        .service-box {
            background: white;
            border-radius: 15px;
            padding: 35px 25px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
            transition: transform 0.3s ease;
            min-height: 220px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .service-box:hover {
            transform: translateY(-5px);
        }

        .service-icon {
            width: 60px;
            height: 60px;
            background: #4dabf7;
            border-radius: 50%;
            margin: 0 auto 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .service-icon::before {
            content: '';
            position: absolute;
            width: 30px;
            height: 30px;
            background: white;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .service-icon::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: #4dabf7;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
        }

        /* Custom icon patterns */
        .service-icon.pattern-1::after {
            background: radial-gradient(circle at 30% 30%, #4dabf7 2px, transparent 2px),
                        radial-gradient(circle at 70% 70%, #4dabf7 2px, transparent 2px),
                        radial-gradient(circle at 50% 50%, #4dabf7 1px, transparent 1px);
            background-size: 8px 8px, 8px 8px, 6px 6px;
        }

        .service-icon.pattern-2::after {
            background: linear-gradient(45deg, #4dabf7 25%, transparent 25%),
                        linear-gradient(-45deg, #4dabf7 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, #4dabf7 75%),
                        linear-gradient(-45deg, transparent 75%, #4dabf7 75%);
            background-size: 4px 4px;
        }

        .service-icon.pattern-3::after {
            background: repeating-linear-gradient(0deg, #4dabf7, #4dabf7 2px, transparent 2px, transparent 4px),
                        repeating-linear-gradient(90deg, #4dabf7, #4dabf7 2px, transparent 2px, transparent 4px);
        }

        .service-icon.pattern-4::after {
            background: conic-gradient(from 0deg, #4dabf7, transparent, #4dabf7, transparent, #4dabf7);
        }

        .service-text {
            color: #6b7280;
            font-size: 0.95rem;
            line-height: 1.6;
            text-align: center;
        }

        /* Responsive Design - Comprehensive Breakpoints */

        /* Desktop (1200px to 1439px) */
        @media (min-width: 1200px) and (max-width: 1439px) {
            .container {
                padding: 70px 25px;
            }
            
            .header-text {
                font-size: 1.15rem;
                margin-bottom: 35px;
            }
            
            .main-title {
                font-size: 3.2rem;
                margin-bottom: 70px;
            }
            
            .services-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 35px;
            }
            
            .service-box {
                padding: 40px 25px;
                min-height: 230px;
            }
            
            .service-icon {
                width: 65px;
                height: 65px;
                margin-bottom: 28px;
            }
        }

        /* Standard Desktop (1025px to 1199px) */
        @media (min-width: 1025px) and (max-width: 1199px) {
            .container {
                padding: 60px 20px;
            }
            
            .services-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 30px;
            }
            
            .service-box {
                padding: 35px 25px;
                min-height: 220px;
            }
        }

        /* Tablet Landscape (769px to 1024px) */
        @media (min-width: 769px) and (max-width: 1024px) {
            .container {
                padding: 50px 20px;
            }
            
            .header-text {
                font-size: 1.05rem;
                margin-bottom: 28px;
            }
            
            .main-title {
                font-size: 2.8rem;
                margin-bottom: 50px;
            }
            
            .services-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
            }
            
            .service-box {
                padding: 32px 22px;
                min-height: 210px;
            }
            
            .service-icon {
                width: 55px;
                height: 55px;
                margin-bottom: 22px;
            }
            
            .service-text {
                font-size: 0.92rem;
            }
        }

        /* Tablet Portrait (768px) */
        @media (max-width: 768px)
            .container {
                padding: 45px 18px;
            }

            .header-text {
                font-size: 1.25rem;
                margin-bottom: 25px;
            }

            .main-title {
                font-size: 2.5rem;
                margin-bottom: 45px;
            }

            .services-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }

            .service-box {
                padding: 28px 18px;
                min-height: 190px;
            }

            .service-icon {
                width: 50px;
                height: 50px;
                margin-bottom: 20px;
            }

            .service-text {
                font-size: 0.9rem;
            }
        }


        /* Mobile Medium (320px to 480px) */
        @media (min-width: 320px) and (max-width: 480px) {
            .container {
                padding: 35px 12px;
            }

            .header-text {
                font-size: 0.9rem;
                margin-bottom: 20px;
            }

            .main-title {
                font-size: 2rem;
                margin-bottom: 35px;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .service-box {
                padding: 22px 15px;
                min-height: 170px;
            }

            .service-icon {
                width: 45px;
                height: 45px;
                margin-bottom: 15px;
            }

            .service-text {
                font-size: 0.85rem;
                line-height: 1.5;
            }

            .page-number {
                bottom: 20px;
                right: 20px;
                width: 35px;
                height: 35px;
                font-size: 0.8rem;
            }
        }

        /* Ultra Small Mobile (up to 319px) */
        @media (max-width: 319px) {
            .container {
                padding: 25px 8px;
            }

            .header-text {
                font-size: 0.8rem;
                margin-bottom: 15px;
                line-height: 1.3;
            }

            .main-title {
                font-size: 1.6rem;
                margin-bottom: 25px;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .service-box {
                padding: 18px 10px;
                min-height: 150px;
            }

            .service-icon {
                width: 35px;
                height: 35px;
                margin-bottom: 10px;
            }

            .service-text {
                font-size: 0.75rem;
                line-height: 1.3;
            }

            .page-number {
                bottom: 12px;
                right: 12px;
                width: 30px;
                height: 30px;
                font-size: 0.7rem;
            }
        } 
        
        .advantages-of-using-pharmacovigilance {
  position: relative;
  background: #fff;
  overflow: hidden;
  padding: 0 0 48px 0;
  min-height: 260px;
}
.advantages-of-using-pharmacovigilance-bg-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 320px;
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transform: translateY(-60px);
  animation: pharmaBgTopDown 1.2s cubic-bezier(0.23, 1, 0.32, 1) both;
}
@keyframes pharmaBgTopDown {
  0% {
    opacity: 0;
    transform: translateY(-60px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.pharma-bg-top-img {
  width: 100%;
  height: auto;
  display: block;
}
.pharma-bg-corner-img {
  position: absolute;
  top: 85px;
  right: 17vw;
  width: 180px;
  height: auto;
  z-index: -1;
  opacity: 0;
  transform: translateX(60px);
  animation: pharmaBgCornerRightToLeft 1.2s cubic-bezier(0.23, 1, 0.32, 1) both;
}
@keyframes pharmaBgCornerRightToLeft {
  0% {
    opacity: 0;
    transform: translateX(60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Tablet styles (768px and below) */
@media screen and (max-width: 768px) {
  .advantages-of-using-pharmacovigilance {
    min-height: 200px;
    padding: 0 0 32px 0;
  }

  .advantages-of-using-pharmacovigilance-bg-top {
    height: 250px;
  }

  .pharma-bg-corner-img {
    top: 30px;
    right: 12vw;
    width: 140px;
  }
}

/* Small tablet / Large mobile (600px and below) */
@media screen and (max-width: 600px) {
  .advantages-of-using-pharmacovigilance {
    min-height: 180px;
    padding: 0 0 24px 0;
  }

  .advantages-of-using-pharmacovigilance-bg-top {
    height: 200px;
  }

  .pharma-bg-corner-img {
    top: 20px;
    right: 8vw;
    width: 120px;
  }
}

/* Mobile styles (480px and below) */
@media screen and (max-width: 480px) {
  .advantages-of-using-pharmacovigilance {
    min-height: 160px;
    padding: 0 0 20px 0;
  }

  .advantages-of-using-pharmacovigilance-bg-top {
    height: 160px;
  }

  .pharma-bg-corner-img {
    top: 10px;
        right: 10vw;
        width: 100px;
  }
}

/* Small mobile (375px and below) */
@media screen and (max-width: 375px) {
  .advantages-of-using-pharmacovigilance {
    min-height: 140px;
    padding: 0 0 16px 0;
  }

  .advantages-of-using-pharmacovigilance-bg-top {
    height: 140px;
  }

  .pharma-bg-corner-img {
    top: 8px;
        right: 11vw;
        width: 70px
  }
}

/* Extra small mobile (320px and below) */
@media screen and (max-width: 320px) {
  .advantages-of-using-pharmacovigilance {
    min-height: 120px;
    padding: 0 0 12px 0;
  }

  .advantages-of-using-pharmacovigilance-bg-top {
    height: 120px;
  }

  .pharma-bg-corner-img {
    top: 20px;
    right: 2vw;
    width: 70px;
  }
}

/* Large screens (1200px and above) */
@media screen and (min-width: 1200px) {
  .advantages-of-using-pharmacovigilance {
    min-height: 300px;
    padding: 0 0 60px 0;
  }

  .advantages-of-using-pharmacovigilance-bg-top {
    height: 380px;
  }

  .pharma-bg-corner-img {
    top: 70px;
    right: 15vw;
    width: 220px;
  }
}

/* Extra large screens (1600px and above) */
@media screen and (min-width: 1600px) {
  .advantages-of-using-pharmacovigilance {
    min-height: 350px;
    padding: 0 0 80px 0;
  }

  .advantages-of-using-pharmacovigilance-bg-top {
    height: 450px;
  }

  .pharma-bg-corner-img {
    top: 120px;
    right: 12vw;
    width: 260px;
  }
} 

.container {
            
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header Section */
        .header-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 60px 0;
            margin-bottom: 80px;
        }

        .header-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .header-text h1 {
            font-size: 3rem;
            font-weight: 300;
            color: #0066cc;
            margin-bottom: 30px;
            letter-spacing: -1px;
        }

        .header-text p {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.8;
        }

        .header-image {
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(0,102,204,0.8), rgba(0,180,216,0.8));
            z-index: 1;
        }

        .laptop-workspace {
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .tech-overlay {
            position: absolute;
            inset: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            z-index: 2;
        }

        .laptop-icon {
            font-size: 4rem;
            color: white;
            z-index: 3;
        }

        .floating-icons {
            position: absolute;
            z-index: 3;
        }

        .floating-icons .icon {
            position: absolute;
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            animation: float 3s ease-in-out infinite;
        }

        .floating-icons .icon:nth-child(1) { top: 20px; right: 20px; animation-delay: 0s; }
        .floating-icons .icon:nth-child(2) { top: 50px; right: 80px; animation-delay: 0.5s; }
        .floating-icons .icon:nth-child(3) { bottom: 60px; right: 30px; animation-delay: 1s; }
        .floating-icons .icon:nth-child(4) { bottom: 20px; left: 20px; animation-delay: 1.5s; }
        .floating-icons .icon:nth-child(5) { top: 80px; left: 30px; animation-delay: 2s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* Services Section */
        .services-section {
            padding: 0 0 80px 0;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 60px;
        }

        .service-section {
            margin-bottom: 50px;
        }

        .service-section h2 {
            font-size: 1.8rem;
            color: #0066cc;
            margin-bottom: 30px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .service-icon-svg {
            width: 40px;
            height: 40px;
            flex-shrink: 0;
        }

        .service-item {
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 20px;
            padding-left: 20px;
        }

        .service-bullet {
            width: 8px;
            height: 8px;
            background: linear-gradient(45deg, #ff6b6b, #ffa726);
            border-radius: 50%;
            margin-top: 8px;
            flex-shrink: 0;
        }

        .service-text {
            font-size: 1rem;
            color: #666;
            line-height: 1.6;
        }

        /* Back to top button */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 1.2rem;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0,102,204,0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .back-to-top:hover {
            background: #0052a3;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,102,204,0.4);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .services-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 50px;
            }

            .service-section h2 {
                font-size: 1.7rem;
            }

            .service-icon-svg {
                width: 35px;
                height: 35px;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
            }

            .header-text h1 {
                font-size: 2.5rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .service-section {
                margin-bottom: 40px;
            }

            .service-section h2 {
                font-size: 1.6rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .service-icon-svg {
                width: 32px;
                height: 32px;
            }

            .laptop-workspace {
                height: 250px;
            }

            .laptop-icon {
                font-size: 3rem;
            }

            .floating-icons .icon {
                width: 35px;
                height: 35px;
                font-size: 1rem;
            }

            .service-item {
                padding: 12px 0;
            }

            .service-text {
                font-size: 0.95rem;
            }
        }

        @media (max-width: 600px) {
            .services-grid {
                gap: 35px;
            }

            .service-section h2 {
                font-size: 1.5rem;
                gap: 12px;
            }

            .service-icon-svg {
                width: 30px;
                height: 30px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 15px;
            }

            .header-section {
                padding: 40px 0;
                margin-bottom: 60px;
            }

            .header-text h1 {
                font-size: 2rem;
                margin-bottom: 20px;
            }

            .header-text p {
                font-size: 1rem;
            }

            .services-section {
                padding: 0 0 60px 0;
            }

            .services-grid {
                gap: 30px;
            }

            .service-section {
                margin-bottom: 30px;
            }

            .service-section h2 {
                font-size: 1.4rem;
                gap: 10px;
            }

            .service-icon-svg {
                width: 28px;
                height: 28px;
            }

            .service-item {
                padding: 10px 0;
                margin-bottom: 15px;
            }

            .service-text {
                font-size: 0.9rem;
            }

            .back-to-top {
                width: 45px;
                height: 45px;
                bottom: 20px;
                right: 20px;
            }
        }

        @media (max-width: 360px) {
            .container {
                padding: 0 12px;
            }

            .header-text h1 {
                font-size: 1.8rem;
            }

            .service-section h2 {
                font-size: 1.3rem;
            }

            .service-icon-svg {
                width: 26px;
                height: 26px;
            }

            .service-item {
                gap: 12px;
            }

            .service-text {
                font-size: 0.85rem;
            }
        }

.expertise-section {
  width: 100%;
  padding: 64px 0 32px 0;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.expertise-heading {
  color: #0072DA;
  font-size: 2.6rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 48px;
  font-family: 'Maven Pro', sans-serif;
}

.expertise-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 48px 36px;
  width: 100%;
  max-width: 1300px;
  margin-bottom: 48px;
}

.expertise-card {
  background: #fff;
  border-radius: 24px;
  border: 1px solid #eaeaea;
  box-shadow: 0 6px 32px rgba(65, 164, 255, 0.13);
  font-family: Georgia, serif;
  font-size: 1.6rem;
  color: #525252;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 36px 36px 36px;
  margin: 0;
  font-weight: 400;
  transition: box-shadow 0.2s;
  text-align: center;
  position: relative;
  height: 100%;
  box-sizing: border-box;
}

.expertise-card:hover {
  box-shadow: 0px 0px 25px 0px rgba(65, 164, 255, 0.3137);
}

.expertise-icon-wrapper {
  position: absolute;
  top: -32px;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 2px 12px rgba(65,164,255,0.10);
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.expertise-icon {
  width: 48px;
  height: 48px;
  display: block;
}

.expertise-description {
  margin-top: 24px;
  font-size: 1.25rem;
  color: #333;
  font-family: Georgia, serif;
  line-height: 1.5;
}

.expertise-btn-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;
}

.expertise-btn {
  background: linear-gradient(90deg, #ff9459 0%, #ff6a18 100%);
  color: #fff;
  font-size: 1.25rem;
  font-weight: 700;
  padding: 22px 48px;
  border-radius: 16px;
  text-decoration: none;
  box-shadow: 0 4px 24px rgba(255,106,24,0.10);
  margin-top: 24px;
  transition: background 0.2s, box-shadow 0.2s;
  letter-spacing: 1px;
}

.expertise-btn:hover {
  background: linear-gradient(90deg, #ff6a18 0%, #ff9459 100%);
  box-shadow: 0 6px 32px rgba(255,106,24,0.18);
}

@media (max-width: 700px) {
  .expertise-heading {
    font-size: 1.5rem;
    margin-bottom: 24px;
  }

  .expertise-grid {
    grid-template-columns: 1fr;
    gap: 28px 0;
    padding: 0 30px;
  }

  .expertise-card {
    padding: 38px 10px 24px 10px;
    min-height: 120px;
  }

  .expertise-icon-wrapper {
    width: 44px;
    height: 44px;
    top: -22px;
  }

  .expertise-icon {
    width: 32px;
    height: 32px;
  }
}


/* CTA Section */
.other-services-cta {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 64px 0 32px 0;
  background: #fff;
  position: relative;
  z-index: 1;
}
.other-services-cta-bg {
  position: relative;
  background: linear-gradient(180deg, #41A4FF 0%, #0072DA 100%);
  border-radius: 36px;
  width: 80vw;
  max-width: 1000px;
  min-height: 300px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 40px rgba(65,164,255,0.10);
  overflow: visible;
}
.other-services-cta-content {
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 56px 24px 48px 24px;
  z-index: 2;
}
.other-services-cta-subtitle {
  color: #fff;
  font-size: 1.50rem;
  font-family: Georgia, serif;
  font-weight: 500;
  text-align: center;
  margin-bottom: 32px;
  letter-spacing: 12px;
}
.other-services-cta-title {
  color: #fff;
  font-size: 3.2rem;
  font-family: 'Maven Pro', sans-serif;
  font-weight: 700;
  text-align: center;
  margin-bottom: 36px;
  margin-top: 1px;
}
.other-services-cta-btn {
  display: inline-block;
  background: white;
  color: rgb(71, 67, 67);
  padding: 25px 40px;
  border-radius: 15px;
  text-decoration: none;
  font-weight: 500;
  font-size: 1.3rem;
  letter-spacing: 1.2px;
  text-transform: uppercase;
  transition: all 1.2s ease;
  box-shadow: 0px 0px 10px 0px #FFFFFF;
  text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
  
}
.other-services-cta-btn:hover {
  background: #ffffff;
  color: #0072DA;
  transform: translateY(-5px);
  box-shadow: 0px 0px 20px 0px #FFFFFF;
  text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}
.other-services-cta-triangle {
  position: absolute;
  z-index: 1;
  width: 180px;
  height: auto;
  pointer-events: none;
}
.other-services-cta-triangle-left {
  left: -120px;
  bottom: 40px;
  z-index: -1;
}
.other-services-cta-triangle-right {
  right: -100px;
  top: 40px;
}
@media (max-width: 900px) {
  .other-services-cta-bg {
    width: 96vw;
    min-height: 260px;
    border-radius: 24px;
  }
  .other-services-cta-title {
    font-size: 2.2rem;
  }
  .other-services-cta-subtitle {
    font-size: 1.2rem;
  }
  .other-services-cta-triangle {
    width: 100px;
  }
  .other-services-cta-triangle-left {
    left: -60px;
    bottom: 10px;
  }
  .other-services-cta-triangle-right {
    right: -40px;
    top: 10px;
  }
}
@media (max-width: 600px) {
  .other-services-cta-bg {
    min-height: 180px;
    padding: 0;
  }
  .other-services-cta-content {
    padding: 24px 4vw;
  }
  .other-services-cta-title {
    font-size: 1.3rem;
    margin-bottom: 18px;
  }
  .other-services-cta-subtitle {
    font-size: 0.95rem;
    margin-bottom: 18px;
  }
  .other-services-cta-btn {
    font-size: 1.1rem;
    padding: 12px 24px;
    border-radius: 12px;
  }
  .other-services-cta-triangle {
    width: 48px;
  }
  .other-services-cta-triangle-left {
    left: -18px;
    bottom: 0px;
  }
  .other-services-cta-triangle-right {
    right: -12px;
    top: 0px;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function () {
  const topSection = document.querySelector('.other-services-top');
  const mainSection = document.querySelector('.other-services-main');
  const mainImg = document.querySelector('.other-services-img');
  const mainTitle = document.querySelector('.other-services-title');
  const mainTexts = document.querySelectorAll('.other-services-text');

  if (window.innerWidth <= 900 && topSection && mainSection) {
    while (mainSection.firstChild) {
      mainSection.removeChild(mainSection.firstChild);
    }
    if (topSection) mainSection.appendChild(topSection);
    if (mainTitle) mainSection.appendChild(mainTitle);
    mainTexts.forEach((el) => mainSection.appendChild(el));
    if (mainImg) mainSection.appendChild(mainImg);
  }

 // Show/hide back to top button based on scroll position
 window.addEventListener('scroll', function() {
            const backToTop = document.querySelector('.back-to-top');
            if (window.pageYOffset > 300) {
                backToTop.style.opacity = '1';
                backToTop.style.visibility = 'visible';
            } else {
                backToTop.style.opacity = '0';
                backToTop.style.visibility = 'hidden';
            }
        });

        // Add smooth scrolling and intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe service sections for animation
        document.querySelectorAll('.service-section').forEach(section => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(30px)';
            section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(section);
        });

        function selectCard(selectedCard) {
            // Remove active class from all cards
            const allCards = document.querySelectorAll('.nav-card');
            allCards.forEach(card => card.classList.remove('active'));
            
            // Add active class to selected card
            selectedCard.classList.add('active');
            
            // Add a subtle animation effect
            selectedCard.style.transform = 'translateY(-8px) scale(1.02)';
            setTimeout(() => {
                selectedCard.style.transform = 'translateY(-8px)';
            }, 150);
        }

        // Add keyboard navigation
        document.addEventListener('keydown', (e) => {
            const cards = document.querySelectorAll('.nav-card');
            const activeCard = document.querySelector('.nav-card.active');
            const activeIndex = Array.from(cards).indexOf(activeCard);
            
            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                e.preventDefault();
                let newIndex;
                
                if (e.key === 'ArrowLeft') {
                    newIndex = activeIndex > 0 ? activeIndex - 1 : cards.length - 1;
                } else {
                    newIndex = activeIndex < cards.length - 1 ? activeIndex + 1 : 0;
                }
                
                selectCard(cards[newIndex]);
            }
        });

        // Add touch support
        let touchStartX = 0;
        let touchEndX = 0;

        document.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
        });

        document.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const cards = document.querySelectorAll('.nav-card');
            const activeCard = document.querySelector('.nav-card.active');
            const activeIndex = Array.from(cards).indexOf(activeCard);
            
            if (touchEndX < touchStartX - swipeThreshold) {
                // Swipe left - next card
                const newIndex = activeIndex < cards.length - 1 ? activeIndex + 1 : 0;
                selectCard(cards[newIndex]);
            }
            
            if (touchEndX > touchStartX + swipeThreshold) {
                // Swipe right - previous card
                const newIndex = activeIndex > 0 ? activeIndex - 1 : cards.length - 1;
                selectCard(cards[newIndex]);
            }
        }
</script>
<?php get_footer(); ?> 